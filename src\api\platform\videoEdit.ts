/**
 * 阿里云ICE剪辑工程相关API
 * 通过后端代理调用阿里云ICE SDK
 */
import config from '@/config';
import { getToken } from '@/utils/auth';
import request from '@/utils/request';

// ============================================================================
// 类型定义 - 根据阿里云ICE官方文档
// ============================================================================
export interface ProjectInfo {
  ProjectId: string;
  Title: string;
  Description?: string;
  Timeline?: any; // Timeline对象，可选
  TemplateId?: string;
  ClipsParam?: string;
  CoverURL?: string;
  Status: string; // 状态为字符串：Draft, Editing, Producing, Produced, ProduceFailed
  StatusName?: string;
  CreateTime?: string;
  ModifiedTime?: string;
  Duration?: number;
  CreateSource?: string;
  ModifiedSource?: string;
  TemplateType?: string;
  BusinessConfig?: any;
  ProjectType?: string;
  BusinessStatus?: string;
}

export interface ProjectListResponse {
  ProjectList: ProjectInfo[];
  NextToken?: string;
  MaxResults?: number;
  RequestId?: string;
}



export interface ProjectListParams {
  keyword?: string;
  status?: string;
  nextToken?: string;
  maxResults?: number;
}



export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 视频合成请求参数
 */
export interface SynthesizeVideoRequest {
  templateId: string;
  clipsParam: string;
  files: Array<{
    name: string;
    uri: string;
  }>;
}

// ============================================================================
// API接口
// ============================================================================


/**
 * 根据模版合成视频
 * @param data 
 * @returns 
 */
export function synthesizeVideo(data: SynthesizeVideoRequest): Promise<ApiResponse<any>> {

  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: `${config.baseUrl}/media/mediaProducing/submit`,
      files: data.files,
      formData: {
        templateId: data.templateId,
        clipsParam: data.clipsParam
      },
      header: {
        'Authorization': `Bearer ${getToken()}`
      },
      success: (res) => {
        if (res.statusCode === 200) {
          const parsedData = JSON.parse(res.data);
          resolve(parsedData);
        }
      },
      fail: (err) => {
        console.error("addAudio上传失败>>>", err);
        reject({
          code: -1,
          msg: "网络请求失败",
          error: err
        });
      },
    })
  });

}

/**
 * 获取剪辑工程列表
 */
export function listEditingProjects(params: ProjectListParams = {}): Promise<ApiResponse<ProjectListResponse>> {
  return request({
    url: '/video/project/listEditingProjects',
    method: 'GET',
    params: {
      keyword: params.keyword,
      status: params.status,
      nextToken: params.nextToken,
      maxResults: params.maxResults || 10
    }
  });
}

